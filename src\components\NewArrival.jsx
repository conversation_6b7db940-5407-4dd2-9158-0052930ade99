import React from "react";

const NewArrival = () => {
  return (
    <div>
      <div className="text-white ml-8 max-w-sm">
        {/* Heading Section */}
        <h4>New Arrivals</h4>
        <h2 className="mt-2 text-2xl md:text-3xl">
          Where art meets your style
        </h2>
        <p className="mt-2">Step into the future of streetwear today.</p>

        {/* Button */}
        <div className="bg-white rounded-4xl h-10 w-40 mt-4 ml-6 flex items-center justify-center">
          <button className="flex items-center space-x-2">
            <span className="text-black font-semibold">NEW DROPS</span>
            <img
              className="w-6 h-6"
              src="./images/right-arrow.svg"
              alt="arrow"
            />
          </button>
        </div>

        {/* Red Box */}
        <div className="bg-red-500 rounded-3xl mt-8 px-4 pb-4">
          {/* Profile Images */}
          <div className="flex justify-center pt-8">
            <img
              src="./images/profile(1).jpg"
              alt="profile1"
              className="w-12 h-12 rounded-full border-2 border-white -mr-3"
            />
            <img
              src="./images/profile(2).jpg"
              alt="profile2"
              className="w-12 h-12 rounded-full border-2 border-white -mr-3 z-10"
            />
            <img
              src="./images/profile(3).jpg"
              alt="profile3"
              className="w-12 h-12 rounded-full border-2 border-white -mr-3 z-20"
            />
            <img
              src="./images/profile(4).jpg"
              alt="profile4"
              className="w-12 h-12 rounded-full border-2 border-white -mr-3 z-30"
            />
          </div>

          {/* Tribe Vibe */}
          <div className="flex items-center justify-center gap-2 py-2 text-white">
            <img className="h-5 w-5" src="./images/star-rainbow.svg" alt="star" />
            <h3 className="text-sm font-medium">Rated 5 stars by Vibe Tribe</h3>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewArrival;
