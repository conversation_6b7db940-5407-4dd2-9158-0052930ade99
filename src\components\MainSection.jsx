import React from "react";
import FeaturedProduct from "./FeaturedProduct";
import NewArrival from "./NewArrival";

const MainSection = () => (
  <>
    {/* Header */}
    <div className="flex justify-between items-center px-8 bg-white text-black">
      <h2 className="text-4xl md:text-6xl font-bold">
        Own the<span className="text-red-400 text-8xl py-4">EDGE</span>
      </h2>
      <h2 className="text-4xl md:text-6xl font-bold text-right">
        Keep the<span className="text-blue-500 text-8xl py-4">VIBE</span>
      </h2>
    </div>

    {/* Model + Featured Product Section */}
    <div className="flex justify-between items-center bg-red-400 rounded-3xl m-8 overflow-visible relative">
      <div>
        <NewArrival/>
      </div>
      {/* Text block */}
      

      {/* Big overflowing model */}
      <div className="relative z-10 overflow-visible">
        <img
          src="./images/model.png"
          alt="model"
          className="
            w-[20rem]
            md:w-[30rem]
            lg:w-[40rem]
            xl:w-[50rem]
            -mt-16
            md:-mt-24
            lg:-mt-32
            object-contain
          "
        />
      </div>

      {/* Featured Product */}
      <div className="mr-8">
        <FeaturedProduct />
      </div>
    </div>
  </>
);

export default MainSection;
