import React from 'react';

const CartButton = () => {
  const cartItemCount = 2;

  return (
    <div className="relative flex items-center border-2 rounded-3xl"> {/* Added padding for better spacing */}
      <img className='pl-2 w-7 h-7' src='./images/shopping-cart.svg' alt='cart' />
      <button className="ml-1 flex items-center"> {/* Added margin-left for spacing */}
        <span className="bg-red-400 text-white rounded-full w-20 h-8 flex items-center justify-center">
          {cartItemCount} <span>products</span> {/* Added margin-left for spacing */}
        </span>
      </button>
    </div>
  );
};

export default CartButton;
