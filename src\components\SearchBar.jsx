import React, { useState } from 'react';

const SearchBar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const toggleSearch = () => {
    setIsOpen(!isOpen);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    console.log('Searching for:', searchTerm);
  };

  return (
    <div className="flex justify-center items-center h-20 relative">
      {/* Search Container */}
      <div className="relative">
        {/* Search Form */}
        <form
          onSubmit={handleSearchSubmit}
          className={`absolute right-0 top-0 flex items-center bg-white border border-gray-300 shadow-lg rounded-full overflow-hidden transition-all duration-300 ease-in-out
            ${isOpen ? 'w-64 opacity-100 visible scale-100' : 'w-0 opacity-0 invisible scale-0'}
          `}
          style={{ transformOrigin: 'right center' }}
        >
          {/* Input Field */}
          <input
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder="Search..."
            className="w-full p-2 pl-4 focus:outline-none focus:ring-2 focus:ring-red-400"
            autoFocus
          />
          {/* Close Button */}
          <button type="button" onClick={toggleSearch} className="p-2 bg-red-400">
            <img src="./images/close-button.svg" alt="close" className="w-4 h-4 " />
          </button>
        </form>

        {/* Search Icon Button */}
        <button
          onClick={toggleSearch}
          className={`p-2 bg-white border border-gray-300 rounded-full shadow transition-all duration-300 ease-in-out
            ${isOpen ? 'opacity-0 invisible' : 'opacity-100 visible'}
          `}
        >
          <img src="./images/search-icon.svg" alt="search" className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default SearchBar;
