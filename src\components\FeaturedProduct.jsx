import React from "react";

const FeaturedProduct = ({
  title = "Urban Vanguard Tee",
  imageSrc = "./images/featured-product-image.jpg",
  alt = "Featured product",
  price = "₹500",
}) => (
  <div className="text-center">
    {/* 1. Icon row with labels */}
    <div className="flex flex-row justify-center items-center space-x-6 mb-6">
      <div className="flex flex-col items-center">
        <img src="/images/tag.svg" alt="Tag" className="w-6 h-6 mb-1" />
        <span className="text-xs text-white font-semibold">Future Threads</span>
      </div>
      <div className="flex flex-col items-center">
        <img src="/images/star.svg" alt="Star" className="w-6 h-6 mb-1" />
        <span className="text-xs text-white font-semibold">Unique Design</span>
      </div>
      <div className="flex flex-col items-center">
        <img src="/images/timer.svg" alt="Timer" className="w-6 h-6 mb-1" />
        <span className="text-xs text-white font-semibold">Limited Drops</span>
      </div>
    </div>

    {/* 2. Heading */}
    <h2 className="text-xl text-white font-bold mb-3 tracking-wide">
      Featured Product
    </h2>

    {/* 3. Product Card */}
    <div className="bg-white pt-4 pb-6 px-4 rounded-2xl shadow-lg max-w-xs mx-auto">
      <img
        src={imageSrc}
        alt={alt}
        className="w-48 h-auto mb-3 rounded-xl mx-auto"
      />

      <h3 className="text-md font-semibold text-gray-900">{title}</h3>
      <p className="text-sm text-gray-600 mb-4">Unmatched comfort</p>

      {/* 4. Button */}
      <button className="flex items-center justify-center bg-red-500 hover:bg-red-600 transition-all text-white font-bold w-24 py-2 rounded-3xl mx-auto space-x-2">
        <span>{price}</span>
        <img
          src="./images/gift.svg"
          alt="Gift"
          className="w-5 h-5 filter invert"
        />
      </button>
    </div>
  </div>
);

export default FeaturedProduct;
